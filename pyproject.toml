[build-system]
requires = ["pdm-backend"]
build-backend = "pdm.backend"

[project]
name = "call-agent-agent"
description = "call-agent agent"
version = "1.0.0"
dependencies = [
    "livekit-agents==1.0.23",
    "livekit-plugins-deepgram==1.0.23",
    "livekit-plugins-elevenlabs==1.0.23",
    "livekit-plugins-openai==1.0.23",
    "livekit-plugins-turn-detector==1.0.23",
    "livekit-plugins-silero==1.0.23",
    "livekit-plugins-playai==1.0.23",
    "livekit-plugins-noise-cancellation==0.2.4",
    "livekit==1.0.8",
    "numpy==2.2.4",
    "protobuf>=5.29.4",
    "aiofiles==24.1.0",
    "aiohappyeyeballs==2.4.4",
    "aiohttp==3.11.11",
    "aiosignal==1.3.2",
    "anyio==4.8.0",
    "attrs==24.2.0",
    "click==8.1.7",
    "frozenlist==1.5.0",
    "idna==3.10",
    "av==14.3.0",
    "multidict==6.1.0",
    "PyJWT==2.10.1",
    "sniffio==1.3.1",
    "typing_extensions==4.12.2",
    "watchfiles==1.0.5",
    "yarl==1.18.0",
    "langfuse==2.60.2",
    "sentry-sdk==2.19.2",
    "annotated-types==0.7.0",
    "certifi==2024.8.30",
    "charset-normalizer==3.4.0",
    "coloredlogs==15.0.1",
    "distro==1.9.0",
    "filelock==3.16.1",
    "flatbuffers==24.3.25",
    "h11==0.14.0",
    "httpcore==1.0.7",
    "httpx==0.28.1",
    "humanfriendly==10.0",
    "Jinja2==3.1.4",
    "jiter==0.8.0",
    "MarkupSafe==3.0.2",
    "mpmath==1.3.0",
    "onnxruntime==1.20.1",
    "openai==1.68.2",
    "packaging==24.2",
    "pillow==11.2.1",
    "propcache==0.2.0",
    "psutil==7.0.0",
    "pydantic==2.10.4",
    "pydantic_core==2.27.2",
    "pydantic_settings==2.8.1",
    "PyYAML==6.0.2",
    "regex==2024.11.6",
    "requests==2.32.3",
    "sympy==1.13.3",
    "tqdm==4.67.1",
    "urllib3==2.3.0",
    "pandas>=2.0.0",
    "python-dotenv>=1.0.0"
]
requires-python = "<3.14,>=3.10"

[project.optional-dependencies]
extra = [
]
[tool.pdm.dev-dependencies]
test = [
]

[tool.pdm]
version = {source = "scm"}
