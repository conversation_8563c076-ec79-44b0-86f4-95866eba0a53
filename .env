
LOG_LEVEL=WARNING
PORT=8000
DEBUG=False
REDIS_PASSWORD=call-agent

MINIO_ROOT_USER=minio-call-agent
MINIO_ROOT_PASSWORD=minio-call-agent-storage

TAVILY_API_KEY=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

WEBHOOK_URL=https://03ce-5-34-1-136.ngrok-free.app/v1/livekit/handler

LIVEKIT_URL=wss://call-agent-60rjsyq2.livekit.cloud
LIVEKIT_API_KEY=APIPqcerBRn49qS
LIVEKIT_API_SECRET=pZ7dt08hv64zLJMnqZf9clve8aEFqWOgUbJ03c1oh8v

LANGFUSE_SECRET_KEY=******************************************
LANGFUSE_PUBLIC_KEY=pk-lf-3c85b460-4a43-422a-9b62-131dbb7f16ee
LANGFUSE_HOST=https://cloud.langfuse.com
LANGFUSE_ENABLE_TRACE=False
OPENAI_API_KEY=********************************************************************************************************************************************************************
LLM_MODEL=gpt-4o

#Jolene-1
DEFAULT_METADATA = '{"type": "outbound-campaign-call", "analysis": {"prompt": "call_metadata: {client_name: , agent_name: , call_date: , call_duration: } | feedback_responses: {first_impression: , needs_assessment: , information_quality: , overall_interaction: , next_steps_clarity: } | satisfaction_assessment: {overall_satisfaction: , notable_positives: [], areas_for_improvement: [], completed_questions: true/false} | call_quality_metrics: {client_engagement: , conversation_flow: , questions_answered: all/partial}"}, "llm": {"name": "Albert", "model": "gpt-4o", "language": "eng", "prompt": "A bank that sells credit cards"}, "tts": {"name": "Camilla", "voiceId": "lSDyBIglyLP8FAMvutxO", "provider": "playai", "details": {"provider": "11labs", "similarity_boost": 0.6, "stability": 0.4, "style": 0, "use_speaker_boost": true}}, "context": {"companyId": "62dd9340-4aed-4c08-93a4-bbdef08248b8", "companyName": "local_bank", "conversationId": "4829de4f-bd9d-45ac-acbb-a15898638cef", "campaignId": "02de8981-622f-4a58-a9f2-f68bfc698995", "timezone": "Asia/Dubai", "participant": {"phoneNumber": "+************", "firstName": "John", "lastName": "Doe", "email": "<EMAIL>", "preferredLanguage": "eng", "customerSegment": "premium", "lastContactDate": "2024-03-20", "notes": "Interested in credit card offers"}, "callSchedule": {"mondayStart": "09:00:00", "mondayEnd": "18:00:00", "tuesdayStart": "09:00:00", "tuesdayEnd": "18:00:00", "wednesdayStart": "09:00:00", "wednesdayEnd": "18:00:00", "thursdayStart": "09:00:00", "thursdayEnd": "18:00:00", "fridayStart": "09:00:00", "fridayEnd": "18:00:00", "saturdayStart": null, "saturdayEnd": null, "sundayStart": null, "sundayEnd": null}}}'
# API Credentials
CORE_API_URL=url
CORE_API_LOGIN=login
CORE_API_PASSWORD=password

CONVERSATION_API_URL=url
CONVERSATION_API_LOGIN=login
CONVERSATION_API_PASSWORD=password

# STT
STT_PROVIDER=deepgram
STT_PLUGIN=deepgram
STT_URL=ws://localhost:43007

# Call Agent Configuration
CALL_AGENT_ID=default-agent

ASSEMBLYAI_API_KEY=********************************
ASSEMBLYAI_MODEL=default

DEEPGRAM_API_KEY=****************************************
DEEPGRAM_MODEL=nova-2


## TTS Providers
VOICE_PROVIDER=playai
ELEVENLABS_API_KEY=***************************************************

#MY
#ELEVENLABS_API_KEY=***************************************************
CARTESIA_API_KEY=sk_car_MdQz1ZdbzrxeojP2Svipaa

TTS_VOICE_ID=21m00Tcm4TlvDq8ikWAM

PLAYHT_USER_ID=kAIhfqsPK1O8QN3Z5yRCxLw7tqD3
PLAYHT_API_KEY=ak-c850d8ef67da457ca13495e1f0061fad
ENVIRONMENT=development

SENTRY_DSN=https://<EMAIL>/4509638663667792
SENTRY_ENVIRONMENT=local

GOOGLE_APPLICATION_CREDENTIALS=speech-api-credentials.json

# Result Handler Configuration
RESULT_HANDLER_ENABLED=true
USE_LIVERICHY_HANDLER=false
SUMMARY_MODEL=gpt-3.5-turbo
ANALYSIS_MODEL=gpt-3.5-turbo
MAX_HISTORY_LENGTH=100
SAVE_INTERMEDIATE_RESULTS=true

#
REDIS_PASSWORD=call-agent
GPU_MODE=False