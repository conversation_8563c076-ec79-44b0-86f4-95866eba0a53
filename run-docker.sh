#!/bin/bash

# Docker Compose Helper Script for AdvAgency
# This script helps you run the application with Docker Compose

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_error ".env file not found! Please create it with your environment variables."
    exit 1
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  up          Start all services"
    echo "  down        Stop all services"
    echo "  build       Build the application image"
    echo "  logs        Show logs from all services"
    echo "  logs-app    Show logs from app service only"
    echo "  restart     Restart all services"
    echo "  clean       Stop services and remove volumes"
    echo "  status      Show status of all services"
    echo ""
}

# Main command handling
case "${1:-up}" in
    "up")
        print_status "Starting all services..."
        docker-compose -f docker-compose-local.yml up --build
        ;;
    "down")
        print_status "Stopping all services..."
        docker-compose -f docker-compose-local.yml down
        ;;
    "build")
        print_status "Building application image..."
        docker-compose -f docker-compose-local.yml build app
        ;;
    "logs")
        print_status "Showing logs from all services..."
        docker-compose -f docker-compose-local.yml logs -f
        ;;
    "logs-app")
        print_status "Showing logs from app service..."
        docker-compose -f docker-compose-local.yml logs -f app
        ;;
    "restart")
        print_status "Restarting all services..."
        docker-compose -f docker-compose-local.yml restart
        ;;
    "clean")
        print_warning "This will stop services and remove volumes. Are you sure? (y/N)"
        read -r response
        if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
            print_status "Cleaning up..."
            docker-compose -f docker-compose-local.yml down -v
            docker system prune -f
        else
            print_status "Cancelled."
        fi
        ;;
    "status")
        print_status "Service status:"
        docker-compose -f docker-compose-local.yml ps
        ;;
    "help"|"-h"|"--help")
        show_usage
        ;;
    *)
        print_error "Unknown command: $1"
        show_usage
        exit 1
        ;;
esac
